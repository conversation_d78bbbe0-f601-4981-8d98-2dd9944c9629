/**
 * NotificationManager.js - Handles all user notifications, error messages, and success messages
 * Provides a centralized way to display feedback to users
 */

class NotificationManager {
    constructor() {
        this.activeToasts = new Set();
        this.maxToasts = 3; // Maximum number of simultaneous toasts
        
        console.log('🔄 NotificationManager: Initializing...');
        this.init();
    }

    /**
     * Initialize notification manager
     */
    init() {
        try {
            // Create toast container if it doesn't exist
            this.createToastContainer();
            
            console.log('✅ NotificationManager: Initialized successfully');
            
        } catch (error) {
            console.error('❌ NotificationManager: Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Create toast container for notifications
     */
    createToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
            `;
            document.body.appendChild(container);
            console.log('✅ NotificationManager: Toast container created');
        }
        this.toastContainer = container;
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showToast(message, 'error');
        console.error('🚨 Error:', message);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
        console.log('✅ Success:', message);
    }

    /**
     * Show info message
     */
    showInfo(message) {
        this.showToast(message, 'info');
        console.log('ℹ️ Info:', message);
    }

    /**
     * Show warning message
     */
    showWarning(message) {
        this.showToast(message, 'warning');
        console.warn('⚠️ Warning:', message);
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 4000) {
        // Remove oldest toast if we have too many
        if (this.activeToasts.size >= this.maxToasts) {
            const oldestToast = this.activeToasts.values().next().value;
            if (oldestToast) {
                this.removeToast(oldestToast);
            }
        }

        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // Get appropriate icon and colors
        const config = this.getToastConfig(type);
        
        toast.style.cssText = `
            background: ${config.background};
            color: ${config.color};
            border-left: 4px solid ${config.borderColor};
            padding: 16px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-size: 14px;
            line-height: 1.4;
            max-width: 100%;
            word-wrap: break-word;
            pointer-events: auto;
            cursor: pointer;
            transform: translateX(100%);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        toast.innerHTML = `
            <span style="font-size: 16px; flex-shrink: 0;">${config.icon}</span>
            <span style="flex: 1;">${message}</span>
            <button style="
                background: none;
                border: none;
                color: inherit;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                margin-left: 10px;
                opacity: 0.7;
                flex-shrink: 0;
            " onclick="this.parentElement.click()">&times;</button>
        `;

        // Add click to dismiss
        toast.addEventListener('click', () => {
            this.removeToast(toast);
        });

        this.toastContainer.appendChild(toast);
        this.activeToasts.add(toast);

        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // Auto remove after duration
        setTimeout(() => {
            this.removeToast(toast);
        }, duration);

        // Add haptic feedback for mobile devices
        if (window.TouchUtils && TouchUtils.isTouchDevice()) {
            TouchUtils.hapticFeedback(type === 'error' ? 'error' : 'success');
        }

        console.log(`📱 NotificationManager: Showed ${type} toast:`, message);
        return toast;
    }

    /**
     * Get toast configuration based on type
     */
    getToastConfig(type) {
        const configs = {
            error: {
                icon: '❌',
                background: '#fee',
                color: '#c53030',
                borderColor: '#f56565'
            },
            success: {
                icon: '✅',
                background: '#f0fff4',
                color: '#38a169',
                borderColor: '#68d391'
            },
            warning: {
                icon: '⚠️',
                background: '#fffbeb',
                color: '#d69e2e',
                borderColor: '#f6e05e'
            },
            info: {
                icon: 'ℹ️',
                background: '#ebf8ff',
                color: '#3182ce',
                borderColor: '#63b3ed'
            }
        };

        return configs[type] || configs.info;
    }

    /**
     * Remove toast notification
     */
    removeToast(toast) {
        if (!toast || !this.activeToasts.has(toast)) return;

        // Animate out
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';

        setTimeout(() => {
            if (toast.parentElement) {
                toast.parentElement.removeChild(toast);
            }
            this.activeToasts.delete(toast);
        }, 300);

        console.log('🗑️ NotificationManager: Removed toast');
    }

    /**
     * Clear all active toasts
     */
    clearAll() {
        this.activeToasts.forEach(toast => {
            this.removeToast(toast);
        });
        console.log('🗑️ NotificationManager: Cleared all toasts');
    }

    /**
     * Show loading notification
     */
    showLoading(message = 'Loading...') {
        const toast = this.showToast(message, 'info', 0); // 0 duration = persistent
        toast.classList.add('loading-toast');
        
        // Add loading spinner
        const spinner = toast.querySelector('span:first-child');
        if (spinner) {
            spinner.innerHTML = '⏳';
            spinner.style.animation = 'spin 1s linear infinite';
        }

        // Add spinner animation if not already present
        if (!document.getElementById('spinner-styles')) {
            const style = document.createElement('style');
            style.id = 'spinner-styles';
            style.textContent = `
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        return toast;
    }

    /**
     * Hide loading notification
     */
    hideLoading() {
        const loadingToasts = document.querySelectorAll('.loading-toast');
        loadingToasts.forEach(toast => {
            this.removeToast(toast);
        });
        console.log('🗑️ NotificationManager: Removed loading toasts');
    }

    /**
     * Get count of active notifications
     */
    getActiveCount() {
        return this.activeToasts.size;
    }

    /**
     * Cleanup method
     */
    destroy() {
        this.clearAll();
        if (this.toastContainer && this.toastContainer.parentElement) {
            this.toastContainer.parentElement.removeChild(this.toastContainer);
        }
        console.log('🗑️ NotificationManager: Destroyed');
    }
}

// Export for use in other modules
window.NotificationManager = NotificationManager;
