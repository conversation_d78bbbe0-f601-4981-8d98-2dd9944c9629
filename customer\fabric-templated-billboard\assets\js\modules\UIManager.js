/**
 * UIManager.js - Handles UI initialization, responsive updates, and general UI state management
 * Responsible for managing the overall UI state and responsive behavior
 */

class UIManager {
    constructor(modules) {
        this.modules = modules;
        this.isInitialized = false;
        
        console.log('🔄 UIManager: Initializing...');
        this.init();
    }

    /**
     * Initialize UI manager
     */
    init() {
        try {
            // Set up responsive UI event listeners
            this.setupResponsiveListeners();
            
            // Set up general UI event handlers
            this.setupGeneralUIHandlers();
            
            this.isInitialized = true;
            console.log('✅ UIManager: Initialized successfully');
            
        } catch (error) {
            console.error('❌ UIManager: Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Set up responsive event listeners
     */
    setupResponsiveListeners() {
        // Listen for responsive changes
        document.addEventListener('responsive:breakpoint:change', (e) => {
            this.handleBreakpointChange(e.detail);
        });

        document.addEventListener('responsive:orientation:change', (e) => {
            this.handleOrientationChange(e.detail);
        });

        console.log('✅ UIManager: Responsive listeners set up');
    }

    /**
     * Set up general UI event handlers
     */
    setupGeneralUIHandlers() {
        // Export button
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                console.log('📤 Export button clicked');
                this.emit('ui:export:requested');
            });
            console.log('✅ UIManager: Export button handler bound');
        } else {
            console.warn('⚠️ UIManager: Export button not found');
        }

        // Clear canvas button
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                console.log('🗑️ Clear button clicked');
                this.emit('ui:clear:requested');
            });
            console.log('✅ UIManager: Clear button handler bound');
        } else {
            console.warn('⚠️ UIManager: Clear button not found');
        }

        console.log('✅ UIManager: General UI handlers set up');
    }

    /**
     * Initialize UI state based on current responsive state
     */
    initializeUIState() {
        console.log('🔄 UIManager: Initializing UI state...');

        // Set up responsive UI classes
        const state = this.modules.responsive.getState();
        this.updateUIForBreakpoint(state);

        // Update canvas UI
        this.updateCanvasUI();

        console.log('✅ UIManager: UI state initialized');
    }

    /**
     * Handle breakpoint changes
     */
    handleBreakpointChange(detail) {
        console.log('UIManager: Breakpoint changed:', detail);
        this.updateUIForBreakpoint(detail.state);
        
        // Adjust canvas for new breakpoint
        if (this.modules.canvas) {
            // Canvas will auto-resize via ResponsiveManager
            setTimeout(() => {
                this.modules.canvas.updateCanvasSize();
            }, 100);
        }

        // Emit event for other modules to respond
        this.emit('ui:breakpoint:changed', detail);
    }

    /**
     * Handle orientation changes
     */
    handleOrientationChange(detail) {
        console.log('UIManager: Orientation changed:', detail);
        
        // Adjust UI layout for orientation
        setTimeout(() => {
            if (this.modules.canvas) {
                this.modules.canvas.updateCanvasSize();
            }
            // Emit event for template grid updates
            this.emit('ui:orientation:changed', detail);
        }, 200);
    }

    /**
     * Update UI for current breakpoint
     */
    updateUIForBreakpoint(state) {
        const container = document.querySelector('.editor-container');
        if (!container) return;
        
        // Add responsive classes
        container.classList.toggle('mobile-layout', state.isMobile);
        container.classList.toggle('tablet-layout', state.isTablet);
        container.classList.toggle('desktop-layout', state.isDesktop);
        
        // Adjust control panel layout
        const controlPanel = document.querySelector('.control-panel');
        if (controlPanel) {
            if (state.isMobile) {
                controlPanel.classList.add('mobile-controls');
            } else {
                controlPanel.classList.remove('mobile-controls');
            }
        }

        console.log('✅ UIManager: UI updated for breakpoint:', state);
    }

    /**
     * Update canvas UI state
     */
    updateCanvasUI() {
        // This method can be expanded to handle canvas-specific UI updates
        console.log('✅ UIManager: Canvas UI updated');
    }

    /**
     * Show or hide UI sections based on content availability
     */
    toggleSection(sectionId, show) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = show ? 'block' : 'none';
            console.log(`✅ UIManager: Section ${sectionId} ${show ? 'shown' : 'hidden'}`);
        }
    }

    /**
     * Update UI section visibility based on template state
     */
    updateSectionVisibility(template) {
        // Show text editing section if template has text
        const hasText = template && template.defaultTexts && template.defaultTexts.length > 0;
        this.toggleSection('textEditingSection', hasText);

        // Show image replacement section if template has default image
        const hasImage = template && template.defaultImage && template.imagePosition;
        this.toggleSection('imageReplacementSection', hasImage);

        console.log('✅ UIManager: Section visibility updated based on template');
    }

    /**
     * Get current UI state
     */
    getUIState() {
        const responsive = this.modules.responsive ? this.modules.responsive.getState() : null;
        
        return {
            isInitialized: this.isInitialized,
            responsive: responsive,
            timestamp: Date.now()
        };
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
        console.log(`📡 UIManager: Emitted event '${eventName}'`, data);
    }

    /**
     * Cleanup method
     */
    destroy() {
        // Remove event listeners if needed
        this.isInitialized = false;
        console.log('🗑️ UIManager: Destroyed');
    }
}

// Export for use in other modules
window.UIManager = UIManager;
