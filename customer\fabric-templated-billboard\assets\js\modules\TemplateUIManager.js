/**
 * TemplateUIManager.js - Handles template selection, category management, and template grid UI
 * Responsible for all template-related user interface interactions
 */

class TemplateUIManager {
    constructor(modules, notificationManager) {
        this.modules = modules;
        this.notificationManager = notificationManager;
        this.isInitialized = false;
        
        console.log('🔄 TemplateUIManager: Initializing...');
        this.init();
    }

    /**
     * Initialize template UI manager
     */
    init() {
        try {
            // Set up template-related event handlers
            this.setupTemplateHandlers();
            
            // Listen for UI events
            this.setupUIEventListeners();
            
            this.isInitialized = true;
            console.log('✅ TemplateUIManager: Initialized successfully');
            
        } catch (error) {
            console.error('❌ TemplateUIManager: Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Set up template-related event handlers
     */
    setupTemplateHandlers() {
        // Category selection
        const categorySelect = document.getElementById('categorySelect');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                console.log('📂 Category changed:', e.target.value);
                this.handleCategoryChange(e.target.value);
            });
            console.log('✅ TemplateUIManager: Category select handler bound');
        } else {
            console.warn('⚠️ TemplateUIManager: Category select element not found');
        }

        // Template selection - Use event delegation for dynamically created elements
        document.addEventListener('click', (e) => {
            // Check if clicked element or its parent is a template option
            const templateOption = e.target.closest('.template-option');
            if (templateOption) {
                console.log('🎨 Template clicked via delegation:', templateOption);
                console.log('🎨 Template data:', {
                    id: templateOption.dataset.templateId,
                    category: templateOption.dataset.category
                });
                this.handleTemplateClick(templateOption);
                return;
            }

            // Also check for direct class match (fallback)
            if (e.target.classList.contains('template-option')) {
                console.log('🎨 Template clicked directly:', e.target);
                this.handleTemplateClick(e.target);
                return;
            }
        });
        console.log('✅ TemplateUIManager: Template click handler bound (event delegation)');
    }

    /**
     * Set up UI event listeners
     */
    setupUIEventListeners() {
        // Listen for orientation changes to update template grid
        document.addEventListener('ui:orientation:changed', () => {
            this.updateTemplateGrid();
        });

        // Listen for breakpoint changes to update template grid
        document.addEventListener('ui:breakpoint:changed', () => {
            this.updateTemplateGrid();
        });

        console.log('✅ TemplateUIManager: UI event listeners set up');
    }

    /**
     * Initialize category dropdown
     */
    initializeCategoryDropdown() {
        const categorySelect = document.getElementById('categorySelect');
        if (!categorySelect) {
            console.warn('⚠️ TemplateUIManager: Category select element not found');
            return;
        }

        if (!this.modules.templates) {
            console.error('❌ TemplateUIManager: Template manager not available');
            return;
        }

        try {
            const categories = this.modules.templates.getCategories();
            console.log(`📂 TemplateUIManager: Found ${categories.length} categories:`, categories);

            // Clear existing options (except the first placeholder)
            while (categorySelect.children.length > 1) {
                categorySelect.removeChild(categorySelect.lastChild);
            }

            // Add category options
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
                categorySelect.appendChild(option);
            });

            console.log('✅ TemplateUIManager: Category dropdown populated');

        } catch (error) {
            console.error('❌ TemplateUIManager: Error initializing category dropdown:', error);
        }
    }

    /**
     * Handle category selection change
     */
    handleCategoryChange(category) {
        console.log('📂 TemplateUIManager: handleCategoryChange called with:', category);

        if (!category) {
            console.log('📂 TemplateUIManager: No category selected, clearing grid');
            this.clearTemplateGrid();
            return;
        }

        try {
            console.log('📂 TemplateUIManager: Category selected:', category);
            this.modules.templates.setCurrentCategory(category);
            this.loadTemplatesForCategory(category);
        } catch (error) {
            console.error('❌ TemplateUIManager: Error handling category change:', error);
            this.notificationManager.showError(`Failed to load category: ${error.message}`);
        }
    }

    /**
     * Load templates for selected category
     */
    loadTemplatesForCategory(category) {
        const templates = this.modules.templates.getTemplatesForCategory(category);
        const templateGrid = document.getElementById('templateGrid');
        
        if (!templateGrid) return;
        
        // Clear existing templates
        templateGrid.innerHTML = '';
        
        // Add templates to grid
        Object.keys(templates).forEach(templateId => {
            const template = templates[templateId];
            const templateElement = this.createTemplateElement(templateId, template);
            templateGrid.appendChild(templateElement);
        });
        
        // Update grid layout for current breakpoint
        this.updateTemplateGrid();
        
        console.log(`✅ TemplateUIManager: Loaded ${Object.keys(templates).length} templates for category: ${category}`);
    }

    /**
     * Create template element for grid
     */
    createTemplateElement(templateId, template) {
        console.log(`🎨 TemplateUIManager: Creating template element: ${templateId}`, template);

        const div = document.createElement('div');
        div.className = 'template-option';
        div.dataset.templateId = templateId;
        div.dataset.category = this.modules.templates.currentCategory;

        // Add direct click handler as backup
        div.addEventListener('click', (e) => {
            console.log('🖱️ TemplateUIManager: Template element clicked directly:', templateId);
            e.stopPropagation(); // Prevent event bubbling
            this.handleTemplateClick(div);
        });

        // Create preview image or placeholder
        const preview = document.createElement('div');
        preview.className = 'template-preview';

        // Use thumbnail if available, otherwise fallback to background
        const imageUrl = template.thumbnail || template.background;
        if (imageUrl) {
            preview.style.backgroundImage = `url(${imageUrl})`;
            preview.style.backgroundSize = 'contain';
            preview.style.backgroundPosition = 'center';
            preview.style.backgroundRepeat = 'no-repeat';
            preview.style.backgroundColor = '#f8f9fa'; // Fallback background for contain
            console.log(`🖼️ TemplateUIManager: Template ${templateId} using image: ${imageUrl} ${template.thumbnail ? '(thumbnail)' : '(background)'}`);
        } else if (template.defaultBackground) {
            preview.style.backgroundColor = template.defaultBackground;
            console.log(`🎨 TemplateUIManager: Template ${templateId} has default background: ${template.defaultBackground}`);
        } else {
            preview.style.backgroundColor = '#f0f0f0';
            console.log(`⚪ TemplateUIManager: Template ${templateId} using fallback background`);
        }

        // Add template info
        const info = document.createElement('div');
        info.className = 'template-info';
        info.innerHTML = `
            <span class="template-type">${template.type || 'unknown'}</span>
            <span class="template-id">${templateId}</span>
        `;

        div.appendChild(preview);
        div.appendChild(info);

        console.log(`✅ TemplateUIManager: Template element created for ${templateId}`);
        return div;
    }

    /**
     * Handle template selection
     */
    handleTemplateClick(element) {
        console.log('🎨 TemplateUIManager: handleTemplateClick called with:', element);

        const templateId = element.dataset.templateId;
        const category = element.dataset.category;

        console.log('📋 TemplateUIManager: Template data:', { templateId, category });

        if (!templateId || !category) {
            console.error('❌ TemplateUIManager: Missing template data:', { templateId, category });
            this.notificationManager.showError('Template data missing. Please try selecting again.');
            return;
        }

        console.log('✅ TemplateUIManager: Template selected:', templateId, category);

        // Update visual selection
        document.querySelectorAll('.template-option').forEach(el => {
            el.classList.remove('selected');
        });
        element.classList.add('selected');
        console.log('✅ TemplateUIManager: Visual selection updated');

        // Emit template selection event
        this.emit('template:selected', { category, templateId, element });
    }

    /**
     * Clear template grid
     */
    clearTemplateGrid() {
        const templateGrid = document.getElementById('templateGrid');
        if (templateGrid) {
            templateGrid.innerHTML = '';
            console.log('✅ TemplateUIManager: Template grid cleared');
        }
    }

    /**
     * Update template grid layout for current breakpoint
     */
    updateTemplateGrid() {
        const templateGrid = document.getElementById('templateGrid');
        if (!templateGrid) return;

        // This method can be expanded to handle responsive grid layouts
        console.log('✅ TemplateUIManager: Template grid layout updated');
    }

    /**
     * Get currently selected template
     */
    getSelectedTemplate() {
        const selectedElement = document.querySelector('.template-option.selected');
        if (selectedElement) {
            return {
                templateId: selectedElement.dataset.templateId,
                category: selectedElement.dataset.category,
                element: selectedElement
            };
        }
        return null;
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
        console.log(`📡 TemplateUIManager: Emitted event '${eventName}'`, data);
    }

    /**
     * Cleanup method
     */
    destroy() {
        this.isInitialized = false;
        console.log('🗑️ TemplateUIManager: Destroyed');
    }
}

// Export for use in other modules
window.TemplateUIManager = TemplateUIManager;
