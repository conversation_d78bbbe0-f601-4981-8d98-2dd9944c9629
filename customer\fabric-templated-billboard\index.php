<?php
// Set page variables for shared header
$pageTitle = 'Billboard Template Editor - Borges Media';
$headerTitle = 'Billboard Template Editor';
$headerSubtitle = 'Create stunning billboards with ease';
$headerIcon = 'fas fa-palette';

// Include shared header
include '../shared/header.php';
?>

    <!-- Fabric.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>

    <!-- Mobile-first responsive styles -->
     <!-- refactor the css please -->
    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.5;
            color: #555;
            background-color: #f8f9fa;
            overflow-x: hidden;
            overscroll-behavior: none;
        }

        /* Mobile-first responsive variables */
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;

            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,123,255,0.1);
            --transition: all 0.3s ease;

            /* Touch-friendly sizes */
            --touch-target: 44px;
            --button-height: 48px;
            --input-height: 44px;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }

        /* Container and Layout */
        .editor-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 100vw;
        }

        /* Header styles removed - using shared header */

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: var(--spacing-md);
            gap: var(--spacing-md);
        }

        /* Canvas Container */
        .canvas-section {
            background: white;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            box-shadow: var(--shadow);
            order: 2;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            background: #fafafa;
            border-radius: var(--border-radius);
            position: relative;
        }

        #billboard-canvas {
            border: 2px solid #007bff;
            border-radius: 4px;
            max-width: 100%;
            height: auto;
        }

        /* Control Panel */
        .control-panel {
            background: white;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            box-shadow: var(--shadow);
            order: 1;
        }

        .control-section {
            margin-bottom: var(--spacing-lg);
        }

        .control-section:last-child {
            margin-bottom: 0;
        }

        .control-section h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--secondary-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Form Controls */
        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        label {
            display: block;
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            color: #555;
        }

        select, input, button {
            width: 100%;
            height: var(--input-height);
            padding: 0 var(--spacing-md);
            border: 2px solid #e0e0e0;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
            -webkit-appearance: none;
            appearance: none;
        }

        select:focus, input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        /* Buttons */
        button {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
            cursor: pointer;
            height: var(--button-height);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        button.secondary {
            background: var(--secondary-color);
        }

        button.danger {
            background: var(--danger-color);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        /* Template Grid - Horizontal Scrollable Layout */
        .template-grid {
            display: flex;
            flex-direction: row;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
            overflow-x: auto;
            overflow-y: hidden;
            padding-bottom: var(--spacing-sm);
            /* Custom scrollbar styling for better UX */
            scrollbar-width: thin;
            scrollbar-color: #ccc #f0f0f0;
        }

        /* Webkit scrollbar styling for desktop browsers */
        .template-grid::-webkit-scrollbar {
            height: 8px;
        }

        .template-grid::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 4px;
        }

        .template-grid::-webkit-scrollbar-thumb {
            background: #ccc;
            border-radius: 4px;
        }

        .template-grid::-webkit-scrollbar-thumb:hover {
            background: #999;
        }

        .template-option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: var(--border-radius);
            overflow: hidden;
            cursor: pointer;
            transition: var(--transition);
            flex-shrink: 0;
            /* Mobile-first sizing - 16:9 aspect ratio */
            min-width: 160px;
            max-width: 200px;
            width: 180px;
            /* Ensure proper flex layout for preview and info sections */
            display: flex;
            flex-direction: column;
        }

        .template-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .template-option.selected {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .template-preview {
            /* 16:9 aspect ratio for landscape thumbnails */
            height: 101px; /* 180px * 9/16 ≈ 101px */
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            background-color: #f8f9fa;
            /* Ensure preview doesn't shrink */
            flex-shrink: 0;
            /* Ensure proper sizing */
            width: 100%;
            min-height: 101px;
        }

        .template-info {
            padding: var(--spacing-sm);
            font-size: 0.875rem;
            /* Ensure info section doesn't interfere with preview sizing */
            flex-shrink: 0;
            min-height: 0;
        }

        .template-type {
            display: block;
            font-weight: 600;
            color: var(--primary-color);
            text-transform: capitalize;
            /* Prevent text from wrapping and affecting layout */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .template-id {
            display: block;
            color: var(--secondary-color);
            font-size: 0.75rem;
            /* Prevent text from wrapping and affecting layout */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Properties Panel */
        .properties-panel {
            background: white;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            box-shadow: var(--shadow);
            display: none;
            order: 3;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-lg);
        }

        .action-buttons button {
            flex: 1;
        }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #ccc;
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */

        /* Tablet Layout */
        @media (min-width: 768px) {
            .main-content {
                flex-direction: row;
                gap: var(--spacing-lg);
            }

            .control-panel {
                width: 300px;
                flex-shrink: 0;
                order: 1;
            }

            .canvas-section {
                flex: 1;
                order: 2;
            }

            .properties-panel {
                width: 250px;
                flex-shrink: 0;
                order: 3;
            }

            .template-option {
                /* Tablet sizing - slightly larger templates */
                min-width: 200px;
                max-width: 240px;
                width: 220px;
            }

            .template-preview {
                /* Adjust height for tablet sizing - maintain 16:9 ratio */
                height: 124px; /* 220px * 9/16 ≈ 124px */
                min-height: 124px;
            }

            .canvas-container {
                min-height: 400px;
            }
        }

        /* Desktop Layout */
        @media (min-width: 1024px) {
            .header h1 {
                font-size: 1.5rem;
            }

            .template-option {
                /* Desktop sizing - larger templates for better visibility */
                min-width: 240px;
                max-width: 280px;
                width: 260px;
            }

            .template-preview {
                /* Adjust height for desktop sizing - maintain 16:9 ratio */
                height: 146px; /* 260px * 9/16 ≈ 146px */
                min-height: 146px;
            }

            .canvas-container {
                min-height: 500px;
            }

            .control-panel {
                width: 350px;
            }

            .properties-panel {
                width: 300px;
            }
        }

        /* Touch Device Optimizations */
        .touch-device button {
            min-height: var(--touch-target);
        }

        .touch-device .template-option {
            /* Touch-friendly sizing - slightly larger for easier tapping */
            min-width: 180px;
            max-width: 220px;
            width: 200px;
        }

        .touch-device .template-preview {
            /* Maintain 16:9 ratio for touch devices */
            height: 113px; /* 200px * 9/16 ≈ 113px */
            min-height: 113px;
        }

        /* Keyboard Open State (Mobile) */
        .keyboard-open .canvas-section {
            display: none;
        }

        /* High DPI Displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            #billboard-canvas {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }

        /* Light Theme Only - Dark Mode Disabled */

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus Indicators */
        button:focus-visible, select:focus-visible, input:focus-visible, .template-option:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Debug styles for template layout (can be removed in production) */
        .template-grid {
            /* Add subtle border to visualize the scrollable area */
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
        }

        .template-option {
            /* Add subtle border to visualize individual template containers */
            position: relative;
        }

        .template-option::after {
            /* Add a subtle indicator for debugging aspect ratios */
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: rgba(0, 123, 255, 0.3);
            border-radius: 50%;
            pointer-events: none;
            opacity: 0.5;
        }

        /* Text Customization Panel */
        .text-customization-panel, .image-upload-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
            z-index: 1000;
            max-width: 700px;
            width: 90vw;
            max-height: 85vh;
            overflow: hidden;
            display: none;
        }

        .panel-header {
            background: var(--primary-color);
            color: white;
            padding: var(--spacing-md);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-header h3 {
            margin: 0;
            font-size: 1.1rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .panel-content {
            padding: var(--spacing-md);
            overflow-y: auto;
            max-height: calc(85vh - 120px);
        }

        /* Grid Layout for Form */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group.checkbox-group {
            align-items: flex-start;
            justify-content: center;
        }

        .panel-content label {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            color: #555;
            font-size: 0.9rem;
        }

        .panel-content input[type="text"],
        .panel-content input[type="color"],
        .panel-content input[type="range"],
        .panel-content input[type="file"],
        .panel-content select {
            width: 100%;
            padding: var(--spacing-sm);
            border: 2px solid #e0e0e0;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }

        .panel-content input[type="color"] {
            height: 50px;
            padding: 4px;
        }

        /* Range Slider Styling - Cross Browser Compatible */
        .panel-content input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 8px;
            background: #ddd;
            border-radius: 4px;
            outline: none;
            margin: 15px 0;
        }

        /* WebKit/Blink (Chrome, Safari, Edge) */
        .panel-content input[type="range"]::-webkit-slider-track {
            width: 100%;
            height: 8px;
            cursor: pointer;
            background: #ddd;
            border-radius: 4px;
            border: none;
        }

        .panel-content input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            margin-top: -8px; /* Centers thumb on track */
        }

        /* Firefox */
        .panel-content input[type="range"]::-moz-range-track {
            width: 100%;
            height: 8px;
            cursor: pointer;
            background: #ddd;
            border-radius: 4px;
            border: none;
        }

        .panel-content input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Internet Explorer/Edge */
        .panel-content input[type="range"]::-ms-track {
            width: 100%;
            height: 8px;
            cursor: pointer;
            background: transparent;
            border-color: transparent;
            color: transparent;
        }

        .panel-content input[type="range"]::-ms-fill-lower {
            background: #ddd;
            border-radius: 4px;
        }

        .panel-content input[type="range"]::-ms-fill-upper {
            background: #ddd;
            border-radius: 4px;
        }

        .panel-content input[type="range"]::-ms-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Focus states */
        .panel-content input[type="range"]:focus::-webkit-slider-thumb {
            box-shadow: 0 0 0 3px rgba(0,123,255,0.3), 0 2px 4px rgba(0,0,0,0.2);
        }

        .panel-content input[type="range"]:focus::-moz-range-thumb {
            box-shadow: 0 0 0 3px rgba(0,123,255,0.3), 0 2px 4px rgba(0,0,0,0.2);
        }

        .panel-content input:focus,
        .panel-content select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        /* Shadow Controls Styles */
        .shadow-group {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 12px;
            background: #f8f9fa;
        }

        .property-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .property-label {
            font-weight: 600;
            color: #333;
            margin: 0 !important;
        }

        .toggle-btn {
            width: 32px;
            height: 32px;
            border: 2px solid #007bff;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #007bff;
            transition: all 0.2s;
        }

        .toggle-btn.active {
            background: #007bff;
            color: white;
        }

        .toggle-btn:hover {
            background: #007bff;
            color: white;
        }

        .shadow-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 12px;
        }

        .sub-property {
            display: flex;
            flex-direction: column;
        }

        .sub-label {
            font-size: 0.85rem;
            font-weight: 500;
            color: #666;
            margin-bottom: 4px !important;
        }

        .range-slider {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 6px;
            background: #ddd;
            border-radius: 3px;
            outline: none;
            margin: 8px 0;
        }

        /* WebKit/Blink for shadow controls */
        .range-slider::-webkit-slider-track {
            width: 100%;
            height: 6px;
            cursor: pointer;
            background: #ddd;
            border-radius: 3px;
            border: none;
        }

        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            margin-top: -8px; /* Centers thumb on track */
        }

        /* Firefox for shadow controls */
        .range-slider::-moz-range-track {
            width: 100%;
            height: 6px;
            cursor: pointer;
            background: #ddd;
            border-radius: 3px;
            border: none;
        }

        .range-slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Internet Explorer/Edge for shadow controls */
        .range-slider::-ms-track {
            width: 100%;
            height: 6px;
            cursor: pointer;
            background: transparent;
            border-color: transparent;
            color: transparent;
        }

        .range-slider::-ms-fill-lower {
            background: #ddd;
            border-radius: 3px;
        }

        .range-slider::-ms-fill-upper {
            background: #ddd;
            border-radius: 3px;
        }

        .range-slider::-ms-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .range-value {
            font-size: 0.8rem;
            color: #666;
            text-align: center;
            margin-top: 4px;
        }

        .property-select {
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.85rem;
        }

        /* Panel Actions */
        .panel-actions {
            padding: var(--spacing-md);
            border-top: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .apply-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: var(--spacing-sm);
        }

        .apply-btn:hover {
            background: #218838;
        }

        .apply-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .remove-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            width: 100%;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        /* Image Upload Specific Styles */
        .image-preview {
            border: 2px dashed #e0e0e0;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            text-align: center;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }

        .preview-placeholder {
            color: #999;
            font-size: 1rem;
        }

        .preview-info {
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            color: #666;
        }

        .upload-info {
            font-size: 0.75rem;
            color: #666;
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }

        /* Text Fields Container */
        .text-fields-container {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            border: 1px solid #e0e0e0;
        }

        .text-field-group {
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-md);
            border: 1px solid #e0e0e0;
            border-radius: var(--border-radius);
            background: white;
            transition: var(--transition);
        }

        .text-field-group:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }

        .text-field-group label {
            display: block;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
            color: #495057;
            font-size: 14px;
        }

        .text-field-group input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-sizing: border-box;
        }

        .text-field-group input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .text-controls {
            margin-top: 10px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .customize-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .customize-btn:hover {
            background: #0056b3;
        }

        /* Image Replacement Container */
        .image-replacement-container {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            border: 1px solid #e0e0e0;
        }

        .current-image-info {
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-sm);
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        .image-info-text {
            margin: 0;
            color: #666;
            font-size: 14px;
            font-style: italic;
        }

        .image-upload-controls {
            text-align: center;
        }

        .primary-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .primary-btn:hover {
            background: #0056b3;
        }

        .upload-info {
            margin-top: var(--spacing-sm);
        }

        .upload-info small {
            color: #666;
            font-size: 12px;
        }

        /* Background Change Container */
        .background-change-container {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            border: 1px solid #e0e0e0;
        }

        .background-controls {
            text-align: center;
        }

        /* Background Modal Styles */
        .background-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(108, 117, 125, 0.5);
        }

        .modal-content {
            position: relative;
            background: white;
            border-radius: var(--border-radius);
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .modal-header h3 {
            margin: 0;
            color: #495057;
        }

        .modal-body {
            padding: var(--spacing-md);
            max-height: 60vh;
            overflow-y: auto;
        }

        .background-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: var(--spacing-sm);
        }

        .background-option {
            aspect-ratio: 1;
            background-size: cover;
            background-position: center;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .background-option:hover {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6c757d;
            padding: 4px;
            line-height: 1;
        }

        .close-btn:hover {
            color: #495057;
        }

        /* Inline Customize Button Styles */
        .customize-btn-inline {
            background: #007bff !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0 !important;
            box-shadow: 0 2px 4px rgba(0,123,255,0.2) !important;
        }

        .customize-btn-inline:hover {
            background: #0056b3 !important;
            transform: scale(1.05) !important;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3) !important;
        }

        .customize-btn-inline:active {
            transform: scale(0.98) !important;
        }

        .customize-btn-inline:focus {
            outline: 2px solid #007bff !important;
            outline-offset: 2px !important;
        }

        /* Text field with inline button container */
        .text-field-group > div {
            display: flex !important;
            gap: 8px !important;
            align-items: center !important;
            width: 100% !important;
        }

        .text-field-group input[type="text"] {
            flex: 1 !important;
            min-width: 0 !important;
        }

        /* Mobile Responsive for Panels */
        @media (max-width: 768px) {
            .text-customization-panel, .image-upload-panel {
                position: fixed;
                top: auto;
                left: 0;
                right: 0;
                bottom: 0;
                transform: none;
                width: 100%;
                max-width: none;
                border-radius: var(--border-radius) var(--border-radius) 0 0;
                max-height: 85vh;
            }

            .panel-content {
                padding: var(--spacing-md) var(--spacing-md) 0;
                max-height: calc(85vh - 140px);
            }

            /* Single column layout on mobile */
            .form-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-sm);
            }

            .form-group.full-width {
                grid-column: 1;
            }

            .panel-content label {
                font-size: 0.85rem;
            }

            .panel-content input[type="text"],
            .panel-content input[type="color"],
            .panel-content input[type="range"],
            .panel-content select {
                padding: 12px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .panel-content input[type="color"] {
                height: 50px;
            }

            .text-field-group {
                margin-bottom: var(--spacing-sm);
                padding: var(--spacing-sm);
            }

            /* Mobile responsive for inline customize buttons */
            .text-field-group .customize-btn-inline {
                width: 44px !important;
                height: 44px !important;
                padding: 12px !important;
                font-size: 16px !important;
            }

            .panel-actions {
                padding: var(--spacing-sm) var(--spacing-md) calc(var(--spacing-md) + env(safe-area-inset-bottom));
            }

            /* Mobile shadow controls */
            .shadow-controls {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .toggle-btn {
                width: 36px;
                height: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <aside class="control-panel">
                <!-- Category Selection -->
                <div class="control-section">
                    <h3>Template Category</h3>
                    <div class="form-group">
                        <label for="categorySelect">Choose Category:</label>
                        <select id="categorySelect">
                            <option value="">Choose a category...</option>
                        </select>
                    </div>
                </div>

                <!-- Template Grid -->
                <div class="control-section">
                    <h3>Templates</h3>
                    <div id="templateGrid" class="template-grid">
                        <!-- Templates will be populated here -->
                    </div>
                </div>

                <!-- Text Fields Panel (Hidden by default) -->
                <div id="textEditingSection" class="control-section" style="display: none;">
                    <h3><i class="fas fa-edit"></i> Edit Text Content</h3>
                    <div id="textFieldsContainer" class="text-fields-container">
                        <!-- Text fields will be populated here when a template is loaded -->
                    </div>
                </div>

                <!-- Image Replacement Panel (Hidden by default) -->
                <div id="imageReplacementSection" class="control-section" style="display: none;">
                    <h3>🖼️ Replace Template Image</h3>
                    <div class="image-replacement-container">
                        <div class="current-image-info">
                            <p class="image-info-text">Current: Default template image</p>
                        </div>
                        <div class="image-upload-controls">
                            <input type="file" id="imageReplacementInput" accept="image/*" style="display: none;">
                            <button id="selectImageBtn" class="primary-btn">📁 Select New Image</button>
                            <div class="upload-info">
                                <small>Supported: JPG, PNG, GIF, WebP (max 5MB)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Background Change Panel (Hidden by default) -->
                <div id="backgroundChangeSection" class="control-section" style="display: none;">
                    <h3><i class="fas fa-image"></i> Change Background</h3>
                    <div class="background-change-container">
                        <div class="background-controls">
                            <button id="changeBackgroundBtn" class="primary-btn"><i class="fas fa-images"></i> Choose Background</button>
                            <div class="upload-info">
                                <small>Select from available background templates</small>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button id="clearBtn" class="secondary">Clear</button>
                    <button id="exportBtn">Export</button>
                </div>
            </aside>

            <!-- Canvas Section -->
            <section class="canvas-section">
                <div class="canvas-container">
                    <canvas id="billboard-canvas"></canvas>
                </div>
            </section>

            <!-- Properties Panel -->
            <aside id="propertiesPanel" class="properties-panel">
                <div class="control-section">
                    <h3>Object Properties</h3>
                    <div id="objectProperties">
                        <!-- Object properties will be populated here -->
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <!-- Background Selection Modal -->
    <div id="backgroundModal" class="background-modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-images"></i> Choose Background</h3>
                <button class="close-btn" id="closeBackgroundModal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="backgroundGrid" class="background-grid">
                    <!-- Background options will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Load template specifications -->
    <script src="template-specs.js"></script>

    <!-- Load existing template manager -->
    <script src="assets/js/template-manager.js"></script>

    <!-- Load utilities -->
    <script src="assets/js/utils/TouchUtils.js"></script>
    <script src="assets/js/utils/ExportUtils.js"></script>

    <!-- Load core modules -->
    <script src="assets/js/core/ResponsiveManager.js"></script>
    <script src="assets/js/core/CanvasManager.js"></script>
    <script src="assets/js/core/EventHandler.js"></script>

    <!-- Load feature modules -->
    <script src="assets/js/modules/ImageReplacementManager.js"></script>
    <script src="assets/js/modules/BackgroundManager.js"></script>

    <!-- Load main application -->
    <script src="assets/js/main.js"></script>
</body>
</html>