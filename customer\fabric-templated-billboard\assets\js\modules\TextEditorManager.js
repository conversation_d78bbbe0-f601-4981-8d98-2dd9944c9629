/**
 * TextEditorManager.js - Handles text editing functionality, text fields, and text customization
 * Responsible for all text-related editing operations and UI
 */

class TextEditorManager {
    constructor(modules, notificationManager) {
        this.modules = modules;
        this.notificationManager = notificationManager;
        this.textObjects = [];
        this.isInitialized = false;
        
        console.log('🔄 TextEditorManager: Initializing...');
        this.init();
    }

    /**
     * Initialize text editor manager
     */
    init() {
        try {
            // Set up event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ TextEditorManager: Initialized successfully');
            
        } catch (error) {
            console.error('❌ TextEditorManager: Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for template loaded events
        document.addEventListener('template:loaded', (e) => {
            this.handleTemplateLoaded(e.detail);
        });

        console.log('✅ TextEditorManager: Event listeners set up');
    }

    /**
     * Handle template loaded event
     */
    handleTemplateLoaded(detail) {
        const { template } = detail;
        if (template) {
            this.populateTextFields(template);
        }
    }

    /**
     * Populate text fields in the left panel
     */
    populateTextFields(template) {
        const container = document.getElementById('textFieldsContainer');
        const section = document.getElementById('textEditingSection');

        if (!container || !section) {
            console.warn('⚠️ TextEditorManager: Text fields container or section not found');
            return;
        }

        // Show the text editing section
        section.style.display = 'block';

        // Clear existing content
        container.innerHTML = '';

        // Create text fields for each text element
        template.defaultTexts.forEach((defaultText, index) => {
            const fieldGroup = this.createTextFieldGroup(defaultText, index);
            container.appendChild(fieldGroup);
        });

        console.log(`✅ TextEditorManager: Created ${template.defaultTexts.length} text input fields with inline customize buttons`);
    }

    /**
     * Create a text field group for a single text element
     */
    createTextFieldGroup(defaultText, index) {
        const fieldGroup = document.createElement('div');
        fieldGroup.className = 'text-field-group';
        fieldGroup.style.cssText = `
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        `;

        fieldGroup.innerHTML = `
            <label style="
                display: block;
                font-weight: 600;
                margin-bottom: 8px;
                color: #333;
                font-size: 14px;
            ">Text ${index + 1}:</label>
            <div style="
                display: flex;
                gap: 8px;
                align-items: center;
                width: 100%;
            ">
                <input
                    type="text"
                    id="textField${index}"
                    value="${defaultText}"
                    data-text-index="${index}"
                    style="
                        flex: 1;
                        padding: 10px;
                        border: 2px solid #e0e0e0;
                        border-radius: 4px;
                        font-size: 14px;
                        transition: border-color 0.2s;
                        box-sizing: border-box;
                        min-width: 0;
                    "
                    placeholder="Enter text..."
                >
                <button
                    class="customize-btn-inline"
                    data-text-index="${index}"
                    style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 10px 12px;
                        border-radius: 4px;
                        font-size: 14px;
                        cursor: pointer;
                        transition: all 0.2s;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-shrink: 0;
                        width: 40px;
                        height: 40px;
                    "
                    title="Customize Text ${index + 1}"
                >
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        `;

        // Add event listeners
        this.setupTextFieldEvents(fieldGroup, index);

        return fieldGroup;
    }

    /**
     * Set up event listeners for a text field group
     */
    setupTextFieldEvents(fieldGroup, index) {
        // Add real-time text update listener
        const textInput = fieldGroup.querySelector(`#textField${index}`);
        textInput.addEventListener('input', (e) => {
            this.updateTextContent(index, e.target.value);
        });

        // Add focus styling
        textInput.addEventListener('focus', (e) => {
            e.target.style.borderColor = '#007bff';
            e.target.style.boxShadow = '0 0 0 3px rgba(0,123,255,0.1)';
        });

        textInput.addEventListener('blur', (e) => {
            e.target.style.borderColor = '#e0e0e0';
            e.target.style.boxShadow = 'none';
        });

        // Add customize button event listener
        const customizeBtn = fieldGroup.querySelector('.customize-btn-inline');
        customizeBtn.addEventListener('click', () => {
            this.showTextStylePanel(index);
        });

        // Add hover effects for customize button
        customizeBtn.addEventListener('mouseenter', (e) => {
            e.target.style.backgroundColor = '#0056b3';
            e.target.style.transform = 'scale(1.05)';
        });

        customizeBtn.addEventListener('mouseleave', (e) => {
            e.target.style.backgroundColor = '#007bff';
            e.target.style.transform = 'scale(1)';
        });
    }

    /**
     * Update text content in real-time
     */
    updateTextContent(textIndex, newText) {
        if (!this.textObjects || !this.textObjects[textIndex]) {
            console.warn(`⚠️ TextEditorManager: Text object ${textIndex} not found`);
            return;
        }

        const textObject = this.textObjects[textIndex];
        textObject.set('text', newText);

        const canvas = this.modules.canvas.getCanvas();
        canvas.renderAll();

        console.log(`📝 TextEditorManager: Updated text ${textIndex + 1}: "${newText}"`);
    }

    /**
     * Show text style customization panel for specific text
     */
    showTextStylePanel(textIndex) {
        if (!this.textObjects || !this.textObjects[textIndex]) {
            console.warn(`⚠️ TextEditorManager: Text object ${textIndex} not found`);
            return;
        }

        const textObject = this.textObjects[textIndex];

        // Select the text object on canvas
        const canvas = this.modules.canvas.getCanvas();
        canvas.setActiveObject(textObject);
        canvas.renderAll();

        // Show the style customization panel
        this.showTextCustomizationPanel(textObject, textIndex);
    }

    /**
     * Set text objects reference (called from template loader)
     */
    setTextObjects(textObjects) {
        this.textObjects = textObjects;
        console.log(`✅ TextEditorManager: Set ${textObjects.length} text objects`);
    }

    /**
     * Get text object by index
     */
    getTextObject(index) {
        return this.textObjects[index] || null;
    }

    /**
     * Get all text objects
     */
    getAllTextObjects() {
        return this.textObjects;
    }

    /**
     * Clear all text objects
     */
    clearTextObjects() {
        this.textObjects = [];
        console.log('🗑️ TextEditorManager: Cleared all text objects');
    }

    /**
     * Hide text editing section
     */
    hideTextEditingSection() {
        const section = document.getElementById('textEditingSection');
        if (section) {
            section.style.display = 'none';
            console.log('✅ TextEditorManager: Text editing section hidden');
        }
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
        console.log(`📡 TextEditorManager: Emitted event '${eventName}'`, data);
    }

    /**
     * Show text customization panel
     */
    showTextCustomizationPanel(textObject, textIndex = null) {
        // Remove any existing panel
        const existingPanel = document.querySelector('.text-customization-panel');
        if (existingPanel) {
            existingPanel.remove();
        }

        const panel = document.createElement('div');
        panel.className = 'text-customization-panel';
        panel.style.display = 'block';

        // Store reference to text object and index
        panel.dataset.textIndex = textIndex !== null ? textIndex : '';

        panel.innerHTML = this.createCustomizationPanelHTML(textObject, textIndex);

        document.body.appendChild(panel);

        // Add real-time preview listeners
        this.addTextPreviewListeners(textObject, textIndex);

        console.log(`🎨 TextEditorManager: Text customization panel opened for text ${textIndex !== null ? (textIndex + 1) : ''}`);
    }

    /**
     * Create HTML for customization panel
     */
    createCustomizationPanelHTML(textObject, textIndex) {
        return `
            <div class="panel-header">
                <h3><i class="fas fa-palette"></i> Customize Text ${textIndex !== null ? (textIndex + 1) : ''}</h3>
                <button class="close-btn" onclick="this.closest('.text-customization-panel').remove()">&times;</button>
            </div>
            <div class="panel-content">
                <div class="form-grid">
                    <div class="form-group full-width">
                        <label for="textContent">Text Content:</label>
                        <input type="text" id="textContent" value="${textObject.text}" maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="textColor">Font Color:</label>
                        <input type="color" id="textColor" value="${textObject.fill}">
                    </div>

                    <div class="form-group">
                        <label for="fontSize">Font Size: <span id="fontSizeValue">${textObject.fontSize}px</span></label>
                        <input type="range" id="fontSize" min="12" max="72" value="${textObject.fontSize}">
                    </div>

                    <div class="form-group">
                        <label for="fontFamily">Font Family:</label>
                        <select id="fontFamily">
                            <option value="Arial" ${textObject.fontFamily === 'Arial' ? 'selected' : ''}>Arial</option>
                            <option value="Helvetica" ${textObject.fontFamily === 'Helvetica' ? 'selected' : ''}>Helvetica</option>
                            <option value="Times New Roman" ${textObject.fontFamily === 'Times New Roman' ? 'selected' : ''}>Times New Roman</option>
                            <option value="Georgia" ${textObject.fontFamily === 'Georgia' ? 'selected' : ''}>Georgia</option>
                            <option value="Verdana" ${textObject.fontFamily === 'Verdana' ? 'selected' : ''}>Verdana</option>
                            <option value="Trebuchet MS" ${textObject.fontFamily === 'Trebuchet MS' ? 'selected' : ''}>Trebuchet MS</option>
                            <option value="Impact" ${textObject.fontFamily === 'Impact' ? 'selected' : ''}>Impact</option>
                            <option value="Comic Sans MS" ${textObject.fontFamily === 'Comic Sans MS' ? 'selected' : ''}>Comic Sans MS</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="fontWeight">Font Weight:</label>
                        <select id="fontWeight">
                            <option value="100" ${textObject.fontWeight === '100' ? 'selected' : ''}>Thin</option>
                            <option value="300" ${textObject.fontWeight === '300' ? 'selected' : ''}>Light</option>
                            <option value="400" ${textObject.fontWeight === '400' || textObject.fontWeight === 'normal' ? 'selected' : ''}>Normal</option>
                            <option value="500" ${textObject.fontWeight === '500' ? 'selected' : ''}>Medium</option>
                            <option value="600" ${textObject.fontWeight === '600' ? 'selected' : ''}>Semi Bold</option>
                            <option value="700" ${textObject.fontWeight === '700' || textObject.fontWeight === 'bold' ? 'selected' : ''}>Bold</option>
                            <option value="800" ${textObject.fontWeight === '800' ? 'selected' : ''}>Extra Bold</option>
                            <option value="900" ${textObject.fontWeight === '900' ? 'selected' : ''}>Black</option>
                        </select>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="apply-btn" onclick="
                        try {
                            window.textEditorManager.applyTextChanges(this);
                        } catch(e) {
                            console.error('❌ Error calling applyTextChanges:', e);
                        }
                        const panel = this.closest('.text-customization-panel');
                        if (panel) panel.remove();
                    ">Apply Changes</button>
                </div>
            </div>
        `;
    }

    /**
     * Add real-time preview listeners to text customization panel
     */
    addTextPreviewListeners(textObject, textIndex = null) {
        const textContent = document.getElementById('textContent');
        const textColor = document.getElementById('textColor');
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const fontFamily = document.getElementById('fontFamily');
        const fontWeight = document.getElementById('fontWeight');

        if (textContent) {
            textContent.addEventListener('input', (e) => {
                textObject.set('text', e.target.value);
                this.modules.canvas.getCanvas().renderAll();

                // Update corresponding text field in left panel if textIndex is available
                if (textIndex !== null) {
                    const leftPanelField = document.getElementById(`textField${textIndex}`);
                    if (leftPanelField) {
                        leftPanelField.value = e.target.value;
                    }
                }
            });
        }

        if (textColor) {
            textColor.addEventListener('input', (e) => {
                textObject.set('fill', e.target.value);
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        if (fontSize && fontSizeValue) {
            fontSize.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                textObject.set('fontSize', size);
                fontSizeValue.textContent = size + 'px';
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                textObject.set('fontFamily', e.target.value);
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        if (fontWeight) {
            fontWeight.addEventListener('change', (e) => {
                textObject.set('fontWeight', e.target.value);
                this.modules.canvas.getCanvas().renderAll();
            });
        }
    }

    /**
     * Apply text changes from customization panel
     */
    applyTextChanges(button) {
        try {
            console.log('✅ TextEditorManager: Text changes applied successfully');
            this.notificationManager.showSuccess('Text changes applied successfully!');
        } catch (error) {
            console.error('❌ TextEditorManager: Error applying text changes:', error);
            this.notificationManager.showError('Error applying text changes');
        }
    }

    /**
     * Cleanup method
     */
    destroy() {
        this.clearTextObjects();
        this.isInitialized = false;
        console.log('🗑️ TextEditorManager: Destroyed');
    }
}

// Export for use in other modules
window.TextEditorManager = TextEditorManager;
