/**
 * TemplateLoaderManager.js - Handles template loading, background setting, and canvas content management
 * Responsible for loading templates onto the canvas with proper positioning and styling
 */

class TemplateLoaderManager {
    constructor(modules, notificationManager) {
        this.modules = modules;
        this.notificationManager = notificationManager;
        this.isInitialized = false;
        
        console.log('🔄 TemplateLoaderManager: Initializing...');
        this.init();
    }

    /**
     * Initialize template loader manager
     */
    init() {
        try {
            // Set up event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ TemplateLoaderManager: Initialized successfully');
            
        } catch (error) {
            console.error('❌ TemplateLoaderManager: Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for template selection events
        document.addEventListener('template:selected', (e) => {
            this.handleTemplateSelection(e.detail);
        });

        console.log('✅ TemplateLoaderManager: Event listeners set up');
    }

    /**
     * Handle template selection event
     */
    handleTemplateSelection(detail) {
        const { category, templateId } = detail;
        this.loadTemplate(category, templateId);
    }

    /**
     * Load template onto canvas
     */
    loadTemplate(category, templateId) {
        console.log('🔄 TemplateLoaderManager: Loading template:', category, templateId);

        const template = this.modules.templates.getTemplate(category, templateId);
        if (!template) {
            console.error('❌ TemplateLoaderManager: Template not found:', category, templateId);
            this.notificationManager.showError(`Template not found: ${templateId}`);
            return;
        }

        console.log('📋 TemplateLoaderManager: Template data:', template);

        // Check if canvas is available
        if (!this.modules.canvas) {
            console.error('❌ TemplateLoaderManager: Canvas not initialized');
            this.notificationManager.showError('Canvas not ready. Please refresh the page.');
            return;
        }

        try {
            // Clear canvas
            console.log('🗑️ TemplateLoaderManager: Clearing canvas...');
            this.modules.canvas.clear();

            // Set template in template manager
            this.modules.templates.setCurrentCategory(category);
            this.modules.templates.setSelectedTemplate(templateId);

            // Set category in background manager
            this.modules.background.setCurrentCategory(category);

            // Load background
            console.log('🖼️ TemplateLoaderManager: Loading background...');
            this.loadTemplateBackground(template);

            // Load text elements
            console.log('📝 TemplateLoaderManager: Loading text elements...');
            const textObjects = this.loadTemplateTexts(template);

            // Load default image if applicable
            if (template.imagePosition && template.defaultImage) {
                console.log('🖼️ TemplateLoaderManager: Loading default image...');
                this.modules.imageReplacement.loadDefaultImage(template);
            }

            console.log('✅ TemplateLoaderManager: Template loaded successfully');
            
            // Emit template loaded event
            this.emit('template:loaded', { category, templateId, template, textObjects });

        } catch (error) {
            console.error('❌ TemplateLoaderManager: Error in loadTemplate:', error);
            this.notificationManager.showError(`Failed to load template: ${error.message}`);
        }
    }

    /**
     * Load template background
     */
    loadTemplateBackground(template) {
        const canvas = this.modules.canvas.getCanvas();

        if (template.background) {
            console.log('🖼️ TemplateLoaderManager: Loading background image:', template.background);
            this.modules.canvas.setBackgroundImage(template.background, (img) => {
                if (img) {
                    console.log('✅ TemplateLoaderManager: Background image loaded and scaled properly');
                    console.log('📐 TemplateLoaderManager: Image dimensions:', `${img.width}x${img.height}`);
                    console.log('📐 TemplateLoaderManager: Scale applied:', `${img.scaleX}x${img.scaleY}`);
                } else {
                    console.error('❌ TemplateLoaderManager: Failed to load background image');
                }
            });
        } else if (template.defaultBackground) {
            console.log('🎨 TemplateLoaderManager: Setting background color:', template.defaultBackground);
            canvas.setBackgroundColor(template.defaultBackground, canvas.renderAll.bind(canvas));
            console.log('✅ TemplateLoaderManager: Background color set');
        } else {
            console.log('⚪ TemplateLoaderManager: No background specified, using default');
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
        }
    }

    /**
     * Load template text elements
     */
    loadTemplateTexts(template) {
        const canvas = this.modules.canvas.getCanvas();

        if (!template.textPositions || !template.textStyles || !template.defaultTexts) {
            console.warn('⚠️ TemplateLoaderManager: Template missing text data');
            return [];
        }

        console.log(`📝 TemplateLoaderManager: Adding ${template.textPositions.length} text elements`);

        // Store text objects for reference
        const textObjects = [];

        template.textPositions.forEach((position, index) => {
            try {
                const textStyle = template.textStyles[index];
                const defaultText = template.defaultTexts[index];

                if (!textStyle || !defaultText) {
                    console.warn(`⚠️ TemplateLoaderManager: Missing text data for index ${index}`);
                    return;
                }

                const text = new fabric.Text(defaultText, {
                    left: position.x,
                    top: position.y,
                    fill: textStyle.color || '#000000',
                    fontSize: parseInt(textStyle.fontSize) || 24,
                    fontFamily: textStyle.fontFamily || 'Arial',
                    fontWeight: textStyle.fontWeight || 'normal',
                    fontStyle: textStyle.fontStyle || 'normal',
                    shadow: textStyle.textShadow || null,
                    originX: position.align === 'center' ? 'center' : 'left',
                    originY: 'top',
                    textIndex: index // Add index for reference
                });

                canvas.add(text);
                textObjects[index] = text;
                console.log(`✅ TemplateLoaderManager: Added text element ${index + 1}: "${defaultText}"`);

            } catch (error) {
                console.error(`❌ TemplateLoaderManager: Error adding text element ${index}:`, error);
            }
        });

        canvas.renderAll();
        console.log('✅ TemplateLoaderManager: All text elements added');

        return textObjects;
    }

    /**
     * Load template image
     */
    loadTemplateImage(template) {
        const canvas = this.modules.canvas.getCanvas();

        console.log('🖼️ TemplateLoaderManager: Loading template image:', template.defaultImage);
        fabric.Image.fromURL(template.defaultImage, (img) => {
            if (img) {
                // Get current zoom for debugging
                const currentZoom = canvas.getZoom();

                console.log('📐 TemplateLoaderManager: Image positioning:', {
                    position: template.imagePosition,
                    imageSize: `${img.width}x${img.height}`,
                    canvasZoom: currentZoom
                });

                img.set({
                    left: template.imagePosition.x,
                    top: template.imagePosition.y,
                    scaleX: template.imagePosition.width / img.width,
                    scaleY: template.imagePosition.height / img.height
                });

                canvas.add(img);
                canvas.renderAll();
                console.log('✅ TemplateLoaderManager: Template image loaded');
            } else {
                console.error('❌ TemplateLoaderManager: Failed to load template image');
            }
        }, {
            crossOrigin: 'anonymous'
        });
    }

    /**
     * Clear canvas content
     */
    clearCanvas() {
        if (this.modules.canvas) {
            this.modules.canvas.clear();
            console.log('🗑️ TemplateLoaderManager: Canvas cleared');
            
            // Emit canvas cleared event
            this.emit('canvas:cleared');
        }
    }

    /**
     * Get current template state
     */
    getCurrentTemplateState() {
        return {
            category: this.modules.templates ? this.modules.templates.currentCategory : null,
            templateId: this.modules.templates ? this.modules.templates.selectedTemplate : null,
            isLoaded: this.isInitialized
        };
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
        console.log(`📡 TemplateLoaderManager: Emitted event '${eventName}'`, data);
    }

    /**
     * Cleanup method
     */
    destroy() {
        this.isInitialized = false;
        console.log('🗑️ TemplateLoaderManager: Destroyed');
    }
}

// Export for use in other modules
window.TemplateLoaderManager = TemplateLoaderManager;
